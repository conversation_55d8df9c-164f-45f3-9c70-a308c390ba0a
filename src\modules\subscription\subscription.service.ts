import { Injectable } from '@nestjs/common';
import { InjectModel } from '@app/transformers/model.transformer';
import { MongooseModel, MongooseDoc, MongooseID } from '@app/interfaces/mongoose.interface';
import { PaginateResult, PaginateOptions, PaginateQuery } from '@app/utils/paginate';
import { Subscription, SubscriptionStatus } from './subscription.model';

@Injectable()
export class SubscriptionService {
  constructor(
    @InjectModel(Subscription) private readonly subscriptionModel: MongooseModel<Subscription>
  ) {}

  public paginator(
    query: PaginateQuery<Subscription>,
    options: PaginateOptions
  ): Promise<PaginateResult<Subscription>> {
    return this.subscriptionModel.paginate(query, options);
  }

  public create(subscription: Subscription): Promise<MongooseDoc<Subscription>> {
    return this.subscriptionModel.create(subscription);
  }

  public update(subscriptionID: MongooseID, update: Partial<Subscription>): Promise<MongooseDoc<Subscription>> {
    return this.subscriptionModel
      .findByIdAndUpdate(subscriptionID, update, { new: true })
      .exec()
      .then((result) => result || Promise.reject(`Subscription '${subscriptionID}' not found`));
  }

  public cancel(subscriptionID: MongooseID): Promise<MongooseDoc<Subscription>> {
    return this.subscriptionModel
      .findByIdAndUpdate(subscriptionID, { status: SubscriptionStatus.CANCELLED }, { new: true })
      .exec()
      .then((result) => result || Promise.reject(`Subscription '${subscriptionID}' not found`));
  }

  public suspend(subscriptionID: MongooseID): Promise<MongooseDoc<Subscription>> {
    return this.subscriptionModel
      .findByIdAndUpdate(subscriptionID, { status: SubscriptionStatus.SUSPENDED }, { new: true })
      .exec()
      .then((result) => result || Promise.reject(`Subscription '${subscriptionID}' not found`));
  }

  public delete(subscriptionID: MongooseID): Promise<MongooseDoc<Subscription>> {
    return this.subscriptionModel
      .findByIdAndRemove(subscriptionID)
      .exec()
      .then((result) => result || Promise.reject(`Subscription '${subscriptionID}' not found`));
  }
}