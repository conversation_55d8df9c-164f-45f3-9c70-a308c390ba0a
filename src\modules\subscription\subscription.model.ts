import { getProviderByTypegooseClass } from '@app/transformers/model.transformer';
import { prop, plugin, modelOptions } from '@typegoose/typegoose';
import { Exclude } from 'class-transformer';
import { mongoosePaginate } from '@app/utils/paginate';
import { BaseModel } from '@app/models/base.model';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  SUSPENDED = 'suspended',
}

export enum SubscriptionPlan {
  ONE_YEAR = 'one_year',
  LIFETIME = 'lifetime',
}

@plugin(mongoosePaginate)
@modelOptions({
  schemaOptions: {
    collection: 'subscriptions',
    versionKey: false,
    toJSON: { getters: true },
    toObject: { getters: true },
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  }
})
export class Subscription extends BaseModel {
  @prop({ required: true })
  email: string;

  @prop({ enum: SubscriptionPlan, required: true })
  plan: SubscriptionPlan;

  @prop({ required: true })
  start_date: Date;

  @prop()
  expiry_date?: Date; // Optional for Lifetime plans

  @prop({ required: true })
  revenue: number;

  @prop({ enum: SubscriptionStatus, default: SubscriptionStatus.ACTIVE })
  status: SubscriptionStatus;

  @Exclude()
  @prop({ default: Date.now })
  updated_at?: Date
}
export const SubscriptionProvider = getProviderByTypegooseClass(Subscription);