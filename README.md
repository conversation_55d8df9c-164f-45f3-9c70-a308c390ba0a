### Practices

#### 0. Authenticate & Authorization

##### - Email flow

##### - External providers: google, facebook, twitter, linkedin

##### - Example of how to use RBAC, CBAC & CASL

#### 1. Upload single file & multiple files

#### 2. Upload big csv file & process import data into MongoDB

#### 3. Caching with REDIS

#### 4. Socket chat example

#### 5. Monitor with Email, Slack

### Development

```bash
$ pnpm install

# dev
$ pnpm run start:dev

# test
$ pnpm run lint
$ pnpm run test
$ pnpm run test:e2e
$ pnpm run test:cov
$ pnpm run test:watch

# build
$ pnpm run build

# run
$ pnpm run start:prod
```

### License

Licensed under the [MIT](/LICENSE) License.
