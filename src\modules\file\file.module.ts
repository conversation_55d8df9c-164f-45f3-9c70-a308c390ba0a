
import { diskStorage } from 'multer';
import multerS3 from 'multer-s3';
import { S3Client } from '@aws-sdk/client-s3';
import { HttpException, HttpStatus, Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { randomStringGenerator } from '@nestjs/common/utils/random-string-generator.util';
import { FileEntityProvider } from './file.model';
import { FileController } from './file.controller';
import { FileService } from './file.service';
import { AllConfigType } from '@app/config/config.type';
import { BlobServiceClient } from '@azure/storage-blob';

@Module({
    imports: [
        MulterModule.registerAsync({
            inject: [ConfigService],
            useFactory: (configService: ConfigService<AllConfigType>) => {
                const storages = {
                    local: () =>
                        diskStorage({
                            destination: './files',
                            filename: (request, file, callback) => {
                                callback(
                                    null,
                                    `${randomStringGenerator()}.${file.originalname
                                        .split('.')
                                        .pop()
                                        ?.toLowerCase()}`,
                                );
                            },
                        }),
                    s3: () => {
                        const s3 = new S3Client({
                            region: configService.get('file.awsS3Region', { infer: true }),
                            credentials: {
                                accessKeyId: configService.getOrThrow('file.accessKeyId', {
                                    infer: true,
                                }),
                                secretAccessKey: configService.getOrThrow(
                                    'file.secretAccessKey',
                                    { infer: true },
                                ),
                            },
                        });

                        return multerS3({
                            s3: s3,
                            bucket: configService.getOrThrow('file.awsDefaultS3Bucket', {
                                infer: true,
                            }),
                            acl: 'public-read',
                            contentType: multerS3.AUTO_CONTENT_TYPE,
                            key: (request, file, callback) => {
                                callback(
                                    null,
                                    `${randomStringGenerator()}.${file.originalname
                                        .split('.')
                                        .pop()
                                        ?.toLowerCase()}`,
                                );
                            },
                        });
                    },
                    azure: () => {
                        // Custom Multer storage engine for Azure Blob Storage
                        return {
                            _handleFile: async (req, file, cb) => {
                                try {
                                    const config = configService as ConfigService<AllConfigType>;
                                    const connectionString = config.getOrThrow('file.azureStorageConnectionString' as any, { infer: true }) as string;
                                    const containerName = config.getOrThrow('file.azureStorageContainer' as any, { infer: true }) as string;
                                    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
                                    const containerClient = blobServiceClient.getContainerClient(containerName);

                                    const ext = file.originalname.split('.').pop()?.toLowerCase();
                                    const blobName = `${randomStringGenerator()}.${ext}`;
                                    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

                                    // Read file stream into buffer
                                    const chunks: Buffer[] = [];
                                    file.stream.on('data', (chunk: Buffer) => chunks.push(chunk));
                                    file.stream.on('end', async () => {
                                        const buffer = Buffer.concat(chunks);
                                        await blockBlobClient.uploadData(buffer, {
                                            blobHTTPHeaders: { blobContentType: file.mimetype }
                                        });
                                        cb(null, {
                                            path: blobName,
                                            location: blockBlobClient.url,
                                            container: containerName,
                                            blobName,
                                        });
                                    });
                                    file.stream.on('error', (err: Error) => cb(err));
                                } catch (err) {
                                    cb(err);
                                }
                            },
                            _removeFile: (req, file, cb) => {
                                cb(null);
                            }
                        };
                    },
                };

                return {
                    fileFilter: (request, file, callback) => {
                        if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/i)) {
                            return callback(
                                new HttpException(
                                    {
                                        status: HttpStatus.UNPROCESSABLE_ENTITY,
                                        errors: {
                                            file: `cantUploadFileType`,
                                        },
                                    },
                                    HttpStatus.UNPROCESSABLE_ENTITY,
                                ),
                                false,
                            );
                        }

                        callback(null, true);
                    },
                    storage:
                        storages[
                            configService.getOrThrow('file.driver', { infer: true })
                        ](),
                    limits: {
                        fileSize: configService.get('file.maxFileSize', { infer: true }),
                    },
                };
            },
        }),
    ],
    controllers: [FileController],
    providers: [ConfigModule, ConfigService, FileService, FileEntityProvider],
    exports: [FileService],
})
export class FileModule { }
