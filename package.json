{"name": "Ufosu API", "version": "1.0.0", "description": "UFOSU AI API built with NestJS", "license": "MIT", "scripts": {"lint": "eslint \"{src,test}/**/*.ts\"", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "cross-env NODE_ENV=production nest build", "start": "cross-env NODE_ENV=development nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "test": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:cov": "cross-env NODE_ENV=test jest --coverage", "test:debug": "cross-env NODE_ENV=test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "cross-env NODE_ENV=test jest --config ./test/jest-e2e.json", "release": ". ./scripts/release.sh"}, "dependencies": {"@aws-sdk/client-comprehendmedical": "^3.846.0", "@aws-sdk/client-s3": "^3.427.0", "@aws-sdk/client-textract": "^3.846.0", "@azure/storage-blob": "^12.27.0", "@babel/plugin-transform-private-methods": "^7.22.5", "@nestjs/axios": "^4.0.1", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.1.3", "@typegoose/auto-increment": "^3.5.0", "@typegoose/typegoose": "^11.5.1", "@types/multer-s3": "^3.0.3", "akismet-api": "^6.0.0", "apple-signin-auth": "1.7.5", "axios": "^1.5.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "bullmq": "^5.56.4", "chalk": "4.x", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "dayjs": "^1.11.10", "express": "^4.18.2", "fast-xml-parser": "^4.3.2", "fb": "2.0.0", "google-auth-library": "9.1.0", "googleapis": "^126.0.1", "handlebars": "^4.7.8", "helmet": "^7.0.0", "js-base64": "^3.7.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongoose": "~7.8.0", "ms": "^2.1.3", "multer": "1.4.5-lts.1", "multer-s3": "^3.0.1", "nestjs-i18n": "^10.3.7", "node-schedule": "^2.1.1", "nodemailer": "^6.9.5", "passport": "~0.6.0", "passport-anonymous": "^1.0.1", "passport-jwt": "^4.0.1", "redis": "^4.6.10", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "shelljs": "^0.8.5", "socket.io": "^4.8.1", "twitter": "1.7.1", "ua-parser-js": "^1.0.36", "yargs": "^17.7.2"}, "WORKAROUND!": ["@babel/plugin-proposal-private-methods is installed for compodoc v1.1.x", "Issue: https://github.com/compodoc/compodoc/issues/1361"], "devDependencies": {"@compodoc/compodoc": "^1.1.21", "@nestjs/cli": "^10.1.18", "@nestjs/schematics": "^10.0.2", "@nestjs/testing": "^11.1.3", "@types/cookie-parser": "^1.4.4", "@types/express": "^4.17.18", "@types/jest": "29.5.5", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.199", "@types/multer": "^1.4.8", "@types/node": "^18.11.17", "@types/node-schedule": "^2.1.1", "@types/nodemailer": "^6.4.11", "@types/passport": "~1.0.13", "@types/passport-jwt": "^3.0.10", "@types/shelljs": "^0.8.13", "@types/supertest": "^2.0.14", "@types/ua-parser-js": "^0.7.37", "@types/validator": "^13.11.2", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.3", "supertest": "^6.3.3", "ts-jest": "29.1.1", "ts-loader": "^9.5.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}