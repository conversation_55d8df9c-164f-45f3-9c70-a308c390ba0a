import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, Validate } from 'class-validator';
import { Transform } from 'class-transformer';
import { lowerCase } from '@app/transformers/value.transformer';

export class AuthEmailLoginDto {
  @ApiProperty({ example: '<EMAIL>' })
  @Transform(({ value }) => lowerCase(value))
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  password: string;
}
