import { registerAs } from '@nestjs/config';
import { FileConfig } from './config.type';
import { IsEnum, IsOptional, IsString, ValidateIf } from 'class-validator';
import validateConfig from '@app/utils/validate-config';

enum FileDriver {
    LOCAL = 'local',
    S3 = 's3',
    AZURE = 'azure',
}

class EnvironmentVariablesValidator {
    @IsEnum(FileDriver)
    FILE_DRIVER: FileDriver;

    // S3 config
    @ValidateIf((envValues) => envValues.FILE_DRIVER === FileDriver.S3)
    @IsString()
    ACCESS_KEY_ID: string;

    @ValidateIf((envValues) => envValues.FILE_DRIVER === FileDriver.S3)
    @IsString()
    SECRET_ACCESS_KEY: string;

    @ValidateIf((envValues) => envValues.FILE_DRIVER === FileDriver.S3)
    @IsString()
    AWS_DEFAULT_S3_BUCKET: string;

    @ValidateIf((envValues) => envValues.FILE_DRIVER === FileDriver.S3)
    @IsString()
    @IsOptional()
    AWS_DEFAULT_S3_URL: string;

    @ValidateIf((envValues) => envValues.FILE_DRIVER === FileDriver.S3)
    @IsString()
    AWS_S3_REGION: string;

    // Azure config
    @ValidateIf((envValues) => envValues.FILE_DRIVER === FileDriver.AZURE)
    @IsString()
    AZURE_STORAGE_CONNECTION_STRING: string;

    @ValidateIf((envValues) => envValues.FILE_DRIVER === FileDriver.AZURE)
    @IsString()
    AZURE_STORAGE_CONTAINER: string;
}

export default registerAs<FileConfig>('file', () => {
    validateConfig(process.env, EnvironmentVariablesValidator);

    return {
        driver: process.env.FILE_DRIVER ?? 'local',
        accessKeyId: process.env.ACCESS_KEY_ID,
        secretAccessKey: process.env.SECRET_ACCESS_KEY,
        awsDefaultS3Bucket: process.env.AWS_DEFAULT_S3_BUCKET,
        awsDefaultS3Url: process.env.AWS_DEFAULT_S3_URL,
        awsS3Region: process.env.AWS_S3_REGION,
        azureStorageConnectionString: process.env.AZURE_STORAGE_CONNECTION_STRING,
        azureStorageContainer: process.env.AZURE_STORAGE_CONTAINER,
        maxFileSize: 5242880, // 5mb
    };
});
