# Lead API

## Field Naming Convention

This module uses **snake_case** consistently for both database fields and API requests/responses for simplicity.

### Fields (snake_case)
- `first_name`
- `last_name`
- `email`
- `phone`
- `company`
- `status`
- `source`
- `created_at`
- `updated_at`

## API Usage Examples

### 1. Filtering with snake_case
```bash
# Filter by first name
GET /leads?first_name=John

# Filter by last name
GET /leads?last_name=Doe

# Filter by email
GET /leads?email=<EMAIL>

# Filter by company
GET /leads?company=Acme

# Filter by status
GET /leads?status=new

# Filter by source
GET /leads?source=website

# Search by keyword (searches first_name, last_name, email, and company)
GET /leads?keyword=john
```

### 2. Sorting with snake_case
```bash
# Sort by creation date (ascending)
GET /leads?sort=created_at

# Sort by creation date (descending)
GET /leads?sort=-created_at

# Sort by first name
GET /leads?sort=first_name

# Sort by last name
GET /leads?sort=last_name

# Sort by company
GET /leads?sort=company

# Sort by status
GET /leads?sort=status
```

### 3. Combined filtering and sorting
```bash
GET /leads?status=new&sort=-created_at&page=1&per_page=10
GET /leads?source=website&company=Acme&sort=first_name
```

## Response Format

API responses use snake_case for consistency with database fields:

```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "company": "Acme Corp",
  "status": "new",
  "source": "website",
  "id": "507f1f77bcf86cd799439011",
  "created_at": "2024-01-01T00:00:00.000Z",
  "updated_at": "2024-01-15T10:30:00.000Z"
}
```

## Lead Status Values

- `new` - Newly created lead
- `contacted` - Lead has been contacted
- `qualified` - Lead has been qualified as potential customer
- `converted` - Lead has been converted to customer
- `lost` - Lead is no longer viable

## Lead Source Values

- `website` - Lead came from website
- `referral` - Lead came from referral
- `social` - Lead came from social media
- `email` - Lead came from email campaign
- `import` - Lead was imported from external source
- `other` - Lead came from other source

## Special Endpoints

### Mark Lead as Contacted
```bash
POST /leads/:id/contact
```

### Send Lifetime Offer Email
```bash
POST /leads/:id/send-offer
Content-Type: application/json

{
  "offerUrl": "https://example.com/offer",
  "unsubscribeUrl": "https://example.com/unsubscribe"
}
```

## Benefits of snake_case Consistency

- **Simplicity**: No field name conversion needed
- **Consistency**: Database and API use the same field names
- **Maintainability**: Easier to debug and maintain
- **Performance**: No overhead from field transformations
