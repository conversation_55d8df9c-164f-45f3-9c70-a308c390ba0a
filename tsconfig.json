{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "target": "es2017", "incremental": true, "declaration": false, "noImplicitAny": false, "removeComments": true, "lib": ["dom", "es2015", "es2016", "es2017"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "downlevelIteration": true, "strictNullChecks": true, "sourceMap": true, "outDir": "./dist", "baseUrl": "./src", "paths": {"@app": ["./"], "@app/*": ["./*"]}}, "typeAcquisition": {"include": ["jest"]}, "include": ["src/**/*", "test/**/*", "src/modules/file/file.controller.ts"], "exclude": ["node_modules", "dist"], "typeRoots": ["node_modules/@types"]}