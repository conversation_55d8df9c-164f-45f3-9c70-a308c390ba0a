import { Modu<PERSON> } from '@nestjs/common';
import { MailService } from '@app/modules/mail/mail.service';
import { LeadProvider } from './lead.model';
import { LeadService } from './lead.service';
import { LeadController } from './lead.controller';

@Module({
  controllers: [LeadController],
  providers: [LeadProvider, LeadService, MailService],
  exports: [LeadService],
})
export class LeadModule {}