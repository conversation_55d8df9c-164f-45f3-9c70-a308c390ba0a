import { registerAs } from '@nestjs/config';
import { OpenAiConfig } from './config.type';
import { IsString } from 'class-validator';
import validateConfig from '@app/utils/validate-config';

class EnvironmentVariablesValidator {
    @IsString()
    OPENAI_API_MODEL: string;

    @IsString()
    OPENAI_API_KEY: string;
}

export default registerAs<OpenAiConfig>('openAi', () => {
    validateConfig(process.env, EnvironmentVariablesValidator);

    return {
        model: process.env.OPENAI_API_MODEL || 'gpt-3.5-turbo',
        key: process.env.OPENAI_API_KEY!,
    };
});