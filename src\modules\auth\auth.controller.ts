/* eslint-disable @typescript-eslint/no-unused-vars */
import {
    Body,
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    Post,
    UseGuards,
    Patch,
    Delete,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { Api<PERSON><PERSON>erAuth, ApiTags } from '@nestjs/swagger';
import { MongooseDoc } from '@app/interfaces/mongoose.interface';
import { Responser } from '@app/decorators/responser.decorator'
import { LoggedInUser } from '@app/decorators/user.decorator';
import { User } from '@app/modules/user/user.model';
import { AuthEmailLoginDto } from './dto/auth-email-login.dto';
import { AuthForgotPasswordDto } from './dto/auth-forgot-password.dto';
import { AuthConfirmEmailDto } from './dto/auth-confirm-email.dto';
import { AuthResetPasswordDto } from './dto/auth-reset-password.dto';
import { AuthUpdateDto } from './dto/auth-update.dto';
import { AuthRegisterLoginDto } from './dto/auth-register-login.dto';
import { AuthRefreshTokenDto } from './dto/auth-refresh-token.dto';
import { LoginResponseType } from './types/login-response.type';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtRefreshAuthGuard } from './guards/jwt-refresh-auth.guard';

@ApiTags('Auth')
@Controller({
    path: 'auth',
    version: '1',
})
export class AuthController {
    constructor(private readonly service: AuthService) { }

    @ApiBearerAuth()
    @Post('email/login')
    @Responser.handle({ message: 'Authenticated', error: HttpStatus.BAD_REQUEST })
    public login(
        @Body() loginDto: AuthEmailLoginDto,
    ): Promise<LoginResponseType> {
        return this.service.validateLogin(loginDto);
    }

    @ApiBearerAuth()
    @Post('email/register')
    @Responser.handle({ message: 'Register' })
    async register(@Body() createUserDto: AuthRegisterLoginDto): Promise<void> {
        return this.service.register(createUserDto);
    }

    @Post('email/confirm')
    @HttpCode(HttpStatus.NO_CONTENT)
    @Responser.handle({ message: 'Confirm email' })
    async confirmEmail(
        @Body() confirmEmailDto: AuthConfirmEmailDto,
    ): Promise<void> {
        return this.service.confirmEmail(confirmEmailDto.hash);
    }

    @ApiBearerAuth()
    @Post('forgot/password')
    @HttpCode(HttpStatus.NO_CONTENT)
    @Responser.handle({ message: 'Forgot password' })
    async forgotPassword(
        @Body() forgotPasswordDto: AuthForgotPasswordDto,
    ): Promise<void> {
        return this.service.forgotPassword(forgotPasswordDto.email);
    }

    @ApiBearerAuth()
    @Post('reset/password')
    @HttpCode(HttpStatus.NO_CONTENT)
    @Responser.handle({ message: 'Reset password' })
    resetPassword(@Body() resetPasswordDto: AuthResetPasswordDto): Promise<void> {
        return this.service.resetPassword(
            resetPasswordDto.hash,
            resetPasswordDto.password,
        );
    }

    @ApiBearerAuth()
    @Get('me')
    @UseGuards(JwtAuthGuard)
    @Responser.handle({ message: 'Get profile', serialization: User })
    public me(@LoggedInUser() user): Promise<MongooseDoc<User>> {
        return this.service.me(user);
    }

    @ApiBearerAuth()
    @Post('refresh')
    @UseGuards(JwtRefreshAuthGuard)
    @HttpCode(HttpStatus.OK)
    @Responser.handle({ message: 'Refresh token' })
    public refresh(@LoggedInUser() user, @Body() input: AuthRefreshTokenDto): Promise<Omit<LoginResponseType, 'user'>> {
        return this.service.refreshToken(user);
    }

    @ApiBearerAuth()
    @Post('logout')
    @UseGuards(JwtAuthGuard)
    @HttpCode(HttpStatus.NO_CONTENT)
    @Responser.handle({ message: 'Logout' })
    public async logout(@LoggedInUser('id') id): Promise<void> {
        await this.service.logout({ id });
    }

    @ApiBearerAuth()
    @Patch('me')
    @UseGuards(JwtAuthGuard)
    @Responser.handle({ message: 'Update profile', serialization: User })
    public update(
        @LoggedInUser() user,
        @Body() userDto: AuthUpdateDto,
    ): Promise<MongooseDoc<User>> {
        return this.service.update(user, userDto);
    }

    @ApiBearerAuth()
    @Delete('me')
    @UseGuards(JwtAuthGuard)
    @HttpCode(HttpStatus.NO_CONTENT)
    @Responser.handle({ message: 'Delete profile' })
    public async delete(@LoggedInUser('id') id): Promise<void> {
        return this.service.delete(id);
    }
}
