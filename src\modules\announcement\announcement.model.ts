/**
 * @file Announcement model
 * @module module/announcement/model
*/

import { AutoIncrementID } from '@typegoose/auto-increment'
import { prop, plugin, modelOptions } from '@typegoose/typegoose'
import { IsString, IsInt, IsIn, IsDefined, IsNotEmpty } from 'class-validator'
import { generalAutoIncrementIDConfig } from '@app/constants/increment.constant'
import { getProviderByTypegooseClass } from '@app/transformers/model.transformer'
import { mongoosePaginate } from '@app/utils/paginate'
import { PublishState } from '@app/constants/biz.constant'

export const ANNOUNCEMENT_STATES = [PublishState.Draft, PublishState.Published] as const

@plugin(mongoosePaginate)
@plugin(AutoIncrementID, generalAutoIncrementIDConfig)
@modelOptions({
    schemaOptions: {
        versionKey: false,
        timestamps: {
            createdAt: 'created_at',
            updatedAt: 'updated_at'
        }
    }
})
export class Announcement {
    @prop({ type: Number })
    id: number

    @IsString()
    @IsNotEmpty({ message: 'content?' })
    @prop({ required: true, validate: /\S+/ })
    content: string

    @IsIn(ANNOUNCEMENT_STATES)
    @IsInt()
    @IsDefined()
    @prop({ enum: PublishState, default: PublishState.Published, index: true })
    state: PublishState

    @prop({ default: Date.now, immutable: true })
    created_at?: Date

    @prop({ default: Date.now })
    updated_at?: Date
}

export const AnnouncementProvider = getProviderByTypegooseClass(Announcement)
