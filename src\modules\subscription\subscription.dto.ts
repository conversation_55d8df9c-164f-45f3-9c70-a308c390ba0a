import { IsEmail, IsDateString, <PERSON><PERSON><PERSON><PERSON>, IsEnum, IsOptional } from 'class-validator';
import { IntersectionType } from '@nestjs/mapped-types';
import { KeywordQueryDTO } from '@app/models/query.model';
import { PaginateOptionWithHotSortDTO } from '@app/models/paginate.model';
import { SubscriptionStatus, SubscriptionPlan } from './subscription.model';

export class CreateSubscriptionDTO {
  @IsEmail()
  email: string;

  @IsEnum(SubscriptionPlan)
  plan: SubscriptionPlan;

  @IsDateString()
  start_date: string;

  @IsDateString()
  @IsOptional()
  expiry_date?: string; // Optional for Lifetime plans

  @IsNumber()
  revenue: number;
}

export class UpdateSubscriptionDTO {
  @IsEnum(SubscriptionPlan)
  @IsOptional()
  plan?: SubscriptionPlan;

  @IsEnum(SubscriptionStatus)
  @IsOptional()
  status?: SubscriptionStatus;

  @IsDateString()
  @IsOptional()
  expiry_date?: string;

  @IsNumber()
  @IsOptional()
  revenue?: number;
}

export class SubscriptionResponseDTO {
  email: string;
  plan: SubscriptionPlan;
  start_date: Date;
  expiry_date?: Date; // Optional for Lifetime plans
  revenue: number;
  status: SubscriptionStatus;
  id: string;
  created_at: Date;
}

export class SubscriptionPaginateQueryDTO extends IntersectionType(PaginateOptionWithHotSortDTO, KeywordQueryDTO) { }