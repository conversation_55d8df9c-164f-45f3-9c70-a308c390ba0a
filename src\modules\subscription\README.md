# Subscription API

## Field Naming Convention

This module uses **snake_case** consistently for both database fields and API requests/responses for simplicity.

### Fields (snake_case)
- `email`
- `plan`
- `start_date`
- `expiry_date` (optional for Lifetime plans)
- `revenue`
- `status`
- `created_at`
- `updated_at`

## API Usage Examples

### 1. Filtering with snake_case
```bash
# Filter by email
GET /subscriptions?email=<EMAIL>

# Filter by plan
GET /subscriptions?plan=One Year

# Filter by status
GET /subscriptions?status=active

# Search by keyword (searches email)
GET /subscriptions?keyword=john
```

### 2. Sorting with snake_case
```bash
# Sort by creation date (ascending)
GET /subscriptions?sort=created_at

# Sort by creation date (descending)
GET /subscriptions?sort=-created_at

# Sort by email
GET /subscriptions?sort=email

# Sort by expiry date
GET /subscriptions?sort=expiry_date
```

### 3. Combined filtering and sorting
```bash
GET /subscriptions?status=active&sort=-created_at&page=1&per_page=10
GET /subscriptions?plan=Lifetime&status=active
```

## Response Format

API responses use snake_case for consistency with database fields:

### One Year Subscription
```json
{
  "email": "<EMAIL>",
  "plan": "One Year",
  "start_date": "2024-01-01T00:00:00.000Z",
  "expiry_date": "2024-12-31T23:59:59.999Z",
  "revenue": 99.99,
  "status": "active",
  "id": "507f1f77bcf86cd799439011",
  "created_at": "2024-01-01T00:00:00.000Z"
}
```

### Lifetime Subscription
```json
{
  "email": "<EMAIL>",
  "plan": "Lifetime",
  "start_date": "2024-01-01T00:00:00.000Z",
  "expiry_date": null,
  "revenue": 299.99,
  "status": "active",
  "id": "507f1f77bcf86cd799439012",
  "created_at": "2024-01-01T00:00:00.000Z"
}
```

## Subscription Status Values

- `active` - Subscription is currently active
- `expired` - Subscription has expired
- `cancelled` - Subscription has been cancelled
- `suspended` - Subscription has been suspended

## Subscription Plans

- `One Year` - Annual subscription with expiry date
- `Lifetime` - Lifetime subscription (no expiry date)

## Creating Subscriptions

### One Year Subscription
```bash
POST /subscriptions
Content-Type: application/json

{
  "email": "<EMAIL>",
  "plan": "One Year",
  "start_date": "2024-01-01T00:00:00.000Z",
  "expiry_date": "2024-12-31T23:59:59.999Z",
  "revenue": 99.99
}
```

### Lifetime Subscription
```bash
POST /subscriptions
Content-Type: application/json

{
  "email": "<EMAIL>",
  "plan": "Lifetime",
  "start_date": "2024-01-01T00:00:00.000Z",
  "revenue": 299.99
}
```

Note: `expiry_date` is optional and should be omitted for Lifetime plans.

## Special Endpoints

### Cancel Subscription
```bash
POST /subscriptions/:id/cancel
```

### Suspend Subscription
```bash
POST /subscriptions/:id/suspend
```

## Benefits of snake_case Consistency

- **Simplicity**: No field name conversion needed
- **Consistency**: Database and API use the same field names
- **Maintainability**: Easier to debug and maintain
- **Performance**: No overhead from field transformations
