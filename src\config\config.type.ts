export type AppConfig = {
    nodeEnv: string;
    name: string;
    workingDirectory: string;
    frontendDomain?: string;
    backendDomain: string;
    allowedOrigins: string[];
    allowedReferer: string;
    port: number;
    apiPrefix: string;
    fallbackLanguage: string;
    headerLanguage: string;
    defaultCacheTtl?: number;
    adminEmail: string;
};

export type ProjectConfig = {
    name: string;
    author: string;
    version: string;
}

export type AppleConfig = {
    appAudience: string[];
};

export type AuthConfig = {
    secret?: string;
    expires?: string;
    refreshSecret?: string;
    refreshExpires?: string;
};

export type DatabaseConfig = {
    url?: string;
};

export type FacebookConfig = {
    appId?: string;
    appSecret?: string;
};

export type FileConfig = {
    driver: string;
    accessKeyId?: string;
    secretAccessKey?: string;
    awsDefaultS3Bucket?: string;
    awsDefaultS3Url?: string;
    awsS3Region?: string;
    maxFileSize: number;
};

export type GoogleConfig = {
    clientId?: string;
    clientSecret?: string;
};

export type MailConfig = {
    port: number;
    host?: string;
    user?: string;
    password?: string;
    defaultEmail?: string;
    defaultName?: string;
    ignoreTLS: boolean;
    secure: boolean;
    requireTLS: boolean;
};

export type TwitterConfig = {
    consumerKey?: string;
    consumerSecret?: string;
};

export type RedisConfig = {
    namespace?: string;
    host?: string;
    port: number;
    username?: string;
    password?: string;
};

export interface AzureAiConfig {
    visionEndpoint: string;
    visionKey: string;
    languageEndpoint: string;
    languageKey: string;
}

export interface OpenAiConfig {
    model: string;
    key: string;
}

export type AllConfigType = {
    app: AppConfig;
    project: ProjectConfig;
    apple: AppleConfig;
    auth: AuthConfig;
    database: DatabaseConfig;
    redis: RedisConfig;
    facebook: FacebookConfig;
    file: FileConfig;
    google: GoogleConfig;
    mail: MailConfig;
    twitter: TwitterConfig;
    azureAi: AzureAiConfig;
    openAi: OpenAiConfig;
};