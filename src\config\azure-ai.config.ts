import { registerAs } from '@nestjs/config';
import { AzureAiConfig } from './config.type';
import { IsString } from 'class-validator';
import validateConfig from '@app/utils/validate-config';

class EnvironmentVariablesValidator {
    @IsString()
    AZURE_AI_VISION_ENDPOINT: string;

    @IsString()
    AZURE_AI_VISION_KEY: string;

    @IsString()
    AZURE_AI_LANGUAGE_ENDPOINT: string;

    @IsString()
    AZURE_AI_LANGUAGE_KEY: string;
}

export default registerAs<AzureAiConfig>('azureAi', () => {
    validateConfig(process.env, EnvironmentVariablesValidator);

    return {
        visionEndpoint: process.env.AZURE_AI_VISION_ENDPOINT!,
        visionKey: process.env.AZURE_AI_VISION_KEY!,
        languageEndpoint: process.env.AZURE_AI_LANGUAGE_ENDPOINT!,
        languageKey: process.env.AZURE_AI_LANGUAGE_KEY!,
    };
});