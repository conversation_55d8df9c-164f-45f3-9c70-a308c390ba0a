<user_friendly_examples>
  <overview>
    Examples for creating user-focused, practical documentation.
  </overview>

  <writing_principles>
    <principle name="benefits_over_features">
      <bad>The concurrent file read feature uses parallel processing.</bad>
      <good>Read multiple files at once, reducing interruptions.</good>
    </principle>

    <principle name="use_scenarios">
      <bad>This improves efficiency.</bad>
      <good>Instead of approving 10 file reads one-by-one, approve them all at once.</good>
    </principle>

    <principle name="hide_implementation_details">
      <bad>The feature uses a thread pool with configurable concurrency limits.</bad>
      <good>Roo reads up to 100 files at once (changeable in settings).</good>
    </principle>

    <principle name="direct_tone">
      <bad>Users must configure the concurrent file read limit parameter.</bad>
      <good>Adjust how many files <PERSON><PERSON> reads at once in settings.</good>
    </principle>
  </writing_principles>

  <structure_examples>
    <example name="feature_intro">
      <template><![CDATA[
# [Feature Name]

[One-sentence description of what it does.]

### Key Features
- [Benefit 1]
- [Benefit 2]
- [Benefit 3]

---
      ]]></template>
    </example>

    <example name="use_case">
      <template><![CDATA[
## Use Case

**Before**: [Description of the old way]
- [Pain point]
- [Pain point]

**Now**: [Description of the new way]
      ]]></template>
    </example>

    <example name="configuration">
      <template><![CDATA[
## Configuration

Customize this feature in settings:

1. **[Setting Name]**
   - **Does**: [Plain language explanation.]
   - **Default**: [Default value.] (Works for most.)
   - **Change if**: [Specific scenarios to adjust this.]

2. **[Setting Name]**
   - **Does**: [Plain language explanation.]
   - **Default**: [Default value.]
   - **Change if**: [Specific use case.]
      ]]></template>
    </example>

    <example name="faq">
      <template><![CDATA[
## FAQ

**"[User question]"**
- [Direct answer.]
- [Optional tip.]

**"[Another question]"**
- [Direct answer.]
- [Optional link.]
      ]]></template>
    </example>

    <example name="troubleshooting">
      <template><![CDATA[
## Troubleshooting

### [Problem symptom]
**Cause**: [Brief explanation.]
**Fix**: [Immediate solution.]
**Alternate fix**: [Alternative solution.]

### [Another issue]
**Scenario**: [When this happens.]
**Solution**:
1. [Step 1]
2. [Step 2]
      ]]></template>
    </example>

  </structure_examples>

  <tone_examples>
    <explanations>
      <example context="limit">
        <technical>The system imposes a hard limit of 100 concurrent operations.</technical>
        <direct>Roo handles up to 100 files at once.</direct>
      </example>

      <example context="error">
        <technical>Error: Maximum concurrency threshold exceeded.</technical>
        <direct>Too many files requested. Lower the file limit in settings.</direct>
      </example>

      <example context="benefit">
        <technical>Reduces API call overhead through request batching.</technical>
        <direct>Get answers faster by reading all needed files at once.</direct>
      </example>
    </explanations>

    <visuals>
      <emojis>
        <when>Error: ⚠️</when>
        <when>Tip: 💡</when>
        <when>Note: 📝</when>
        <when>Security: 🔒</when>
      </emojis>

      <formatting>
        <bold>For emphasis</bold>
        <code>For settings, file paths, or commands</code>
        <blockquotes>For callouts or warnings</blockquotes>
      </formatting>
    </visuals>
  </tone_examples>

  <real_world_example>
    <title>Concurrent File Reads Doc</title>
    <content><![CDATA[
# Concurrent File Reads

Read multiple files from your workspace in a single step.

### Key Features
- Read up to 100 files in one request.
- Enabled by default for faster workflow.
- Configurable to match system capabilities.

---

## Use Case

**Before**: Multiple, sequential requests to read files:
- "Read `src/app.js`?" → Approve
- "Read `src/utils.js`?" → Approve
- "Read `src/config.json`?" → Approve

**Now**: Roo asks once to read all related files.

## How it Works

Roo automatically identifies and reads relevant files together for tasks requiring multi-file context, such as:

- Understanding components split across multiple files.
- Refactoring code with dependencies.
- Answering questions requiring broad project context.

The [`read_file`](/tools/read-file) tool accepts multiple files in a single request.

---

## Configuration

Customize in Roo's settings:

1. **Enable/Disable Concurrent File Reads**
   - **Does**: Toggles whether Roo can read multiple files at once.
   - **Default**: Enabled.
   - **Disable if**: Using a less capable AI model or requiring more access control.

2. **Concurrent File Reads Limit**
   - **Does**: Sets max number of files Roo can read at once.
   - **Default**: 100.
   - **Adjust**: Lower for memory constraints; raise for very large projects.

---

## FAQ

**"Too many files are requested at once."**
- Lower the file limit in settings.
- Deny individual files in the batch dialog.

**"Some files were denied but others were approved."**
- Normal behavior. Roo works with approved files.
- Files may be blocked by `.rooignore` settings.

**"Does this use more memory?"**
- Yes, but the impact is usually minimal.
- If you see slowdowns, reduce the file limit.

    ]]></content>
  </real_world_example>

  <checklist>
    <item>Does it start with benefits?</item>
    <item>Are technical terms avoided?</item>
    <item>Is the tone direct?</item>
    <item>Are there practical examples?</item>
    <item>Are sections short and scannable?</item>
    <item>Does it answer user questions?</item>
    <item>Is help accessible?</item>
  </checklist>
</user_friendly_examples>