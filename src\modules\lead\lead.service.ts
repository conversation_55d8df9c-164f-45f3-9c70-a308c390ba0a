import { Injectable } from '@nestjs/common';
import { InjectModel } from '@app/transformers/model.transformer';
import { MongooseModel, MongooseDoc, MongooseID } from '@app/interfaces/mongoose.interface';
import { MailService } from '@app/modules/mail/mail.service';
import { PaginateResult, PaginateOptions, PaginateQuery } from '@app/utils/paginate';
import { Lead, LeadStatus } from './lead.model';

@Injectable()
export class LeadService {
  constructor(
    @InjectModel(Lead) private readonly leadModel: MongooseModel<Lead>,
    private readonly mailService: MailService
  ) {}

  public async sendLifetimeOfferToLead(leadId: string, offerUrl: string, unsubscribeUrl: string): Promise<void> {
    const lead = await this.leadModel.findById(leadId).exec();
    if (!lead) throw new Error(`Lead '${leadId}' not found`);
    await this.mailService.sendLifetimeOffer({
      to: lead.email,
      data: {
        lead_name: `${lead.first_name} ${lead.last_name}`,
        offer_url: offerUrl,
        unsubscribe_url: unsubscribeUrl,
      }
    });
    await this.leadModel.findByIdAndUpdate(leadId, {
      status: LeadStatus.CONTACTED,
    }).exec();
  }

  public paginator(
    query: PaginateQuery<Lead>,
    options: PaginateOptions
  ): Promise<PaginateResult<Lead>> {
    return this.leadModel.paginate(query, options);
  }

  public create(lead: Lead): Promise<MongooseDoc<Lead>> {
    return this.leadModel.create(lead);
  }

  public update(leadID: MongooseID, update: Partial<Lead>): Promise<MongooseDoc<Lead>> {
    return this.leadModel
      .findByIdAndUpdate(leadID, update, { new: true })
      .exec()
      .then((result) => result || Promise.reject(`Lead '${leadID}' not found`));
  }

  public markContacted(leadID: MongooseID): Promise<MongooseDoc<Lead>> {
    return this.leadModel
      .findByIdAndUpdate(
        leadID,
        { status: LeadStatus.CONTACTED },
        { new: true }
      )
      .exec()
      .then((result) => result || Promise.reject(`Lead '${leadID}' not found`));
  }
}