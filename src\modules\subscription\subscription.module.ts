import { Module } from '@nestjs/common';
import { SubscriptionProvider } from './subscription.model';
import { SubscriptionService } from './subscription.service';
import { SubscriptionController } from './subscription.controller';

@Module({
  controllers: [SubscriptionController],
  providers: [SubscriptionProvider, SubscriptionService],
  exports: [SubscriptionService],
})
export class SubscriptionModule {}