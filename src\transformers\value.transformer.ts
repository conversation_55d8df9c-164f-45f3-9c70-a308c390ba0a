/**
 * @file Value transformer
 * @module transformer/value
*/

import { isNumberString, isNumber, isBooleanString, isDateString, isDefined } from 'class-validator'

export function unknownToNumber(value: unknown): number | unknown {
  return isNumberString(value) ? Number(value) : value
}

export function unknownToBoolean(value: unknown): boolean | unknown {
  return isBooleanString(value) ? JSON.parse(value as string) : value
}

// https://www.progress.com/blogs/understanding-iso-8601-date-and-time-format
export function unknownToDate(value: unknown): Date | unknown {
  return isDateString(value) ? new Date(value as string) : value
}

export function numberToBoolean(value: number): boolean | number {
  return isNumber(value, {
    allowNaN: false,
    allowInfinity: false
  })
    ? Boolean(value)
    : value
}

export function lowerCase(value: unknown): number | unknown {
  return isDefined(value) ? String(value).toLowerCase().trim() : value
}
