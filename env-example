NODE_ENV=development
APP_PORT=3000
APP_NAME="API"
API_PREFIX=api
APP_FALLBACK_LANGUAGE=en
APP_HEADER_LANGUAGE=x-custom-lang
APP_DEFAULT_CACHE_TTL=0
FRONTEND_DOMAIN=http://localhost:3000
BACKEND_DOMAIN=http://localhost:3000
APP_ADMIN_EMAIL=admin email, e.g. <EMAIL>
ALLOWED_ORIGINS=['http://localhost:3000']
ALLOWED_REFERER=example.com

DATABASE_URL=

# Support "local", "s3"
FILE_DRIVER=local
ACCESS_KEY_ID=
SECRET_ACCESS_KEY=
AWS_S3_REGION=
AWS_DEFAULT_S3_BUCKET=

MAIL_HOST=maildev
MAIL_PORT=1025
MAIL_USER=
MAIL_PASSWORD=
MAIL_IGNORE_TLS=true
MAIL_SECURE=false
MAIL_REQUIRE_TLS=false
MAIL_DEFAULT_EMAIL=<EMAIL>
MAIL_DEFAULT_NAME=Api
MAIL_CLIENT_PORT=1080

AUTH_JWT_SECRET=secret
AUTH_JWT_TOKEN_EXPIRES_IN=15m
AUTH_REFRESH_SECRET=secret_for_refresh
AUTH_REFRESH_TOKEN_EXPIRES_IN=3650d

FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

APPLE_APP_AUDIENCE=[]

TWITTER_CONSUMER_KEY=
TWITTER_CONSUMER_SECRET=

REDIS_NAMESPACE=api
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=

WORKER_HOST=redis://redis:6379/1
