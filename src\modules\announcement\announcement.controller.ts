/**
 * @file Announcement controller
 * @module module/announcement/controller
*/

import lodash from 'lodash'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Controller, Get, Put, Post, Delete, Body, UseGuards, Query } from '@nestjs/common'
import { PermissionPipe } from '@app/pipes/permission.pipe'
import { ExposePipe } from '@app/pipes/expose.pipe'
import { Responser } from '@app/decorators/responser.decorator'
import { QueryParams, QueryParamsResult } from '@app/decorators/queryparams.decorator'
import { PaginateResult, PaginateQuery } from '@app/utils/paginate'
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { AnnouncementsDTO, AnnouncementPaginateQueryDTO } from './announcement.dto'
import { AnnouncementService } from './announcement.service'
import { Announcement } from './announcement.model'

@ApiBearerAuth()
@ApiTags('Announcement')
@UseGuards(JwtAuthGuard)
@Controller('announcement')
export class AnnouncementController {
  constructor(private readonly announcementService: AnnouncementService) { }

  @Get()
  @Responser.paginate()
  @Responser.handle('Get announcements')
  getAnnouncements(
    @Query(PermissionPipe, ExposePipe) query: AnnouncementPaginateQueryDTO
  ): Promise<PaginateResult<Announcement>> {
    const { sort, page, per_page, ...filters } = query
    const { keyword, state } = filters
    const paginateQuery: PaginateQuery<Announcement> = {}

    // search
    if (keyword) {
      paginateQuery.content = new RegExp(lodash.trim(keyword), 'i')
    }

    // state
    if (state != null) {
      paginateQuery.state = state
    }

    // paginator
    return this.announcementService.paginator(paginateQuery, {
      page,
      perPage: per_page,
      dateSort: sort
    })
  }

  @Post()
  @Responser.handle('Create announcement')
  createAnnouncement(@Body() announcement: Announcement) {
    return this.announcementService.create(announcement)
  }

  @Delete()
  @Responser.handle('Delete announcements')
  delAnnouncements(@Body() body: AnnouncementsDTO) {
    return this.announcementService.batchDelete(body.announcement_ids)
  }

  @Put(':id')
  @Responser.handle('Update announcement')
  putAnnouncement(@QueryParams() { params }: QueryParamsResult, @Body() announcement: Announcement) {
    return this.announcementService.update(params.id, announcement)
  }

  @Delete(':id')
  @Responser.handle('Delete announcement')
  delAnnouncement(@QueryParams() { params }: QueryParamsResult) {
    return this.announcementService.delete(params.id)
  }
}
